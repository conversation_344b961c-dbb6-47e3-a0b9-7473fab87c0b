{"time":"2025-07-31T22:55:49.311865933+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-31T22:55:49.664961498+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-31T22:55:49.665394524+08:00","level":"INFO","msg":"stream: created new stream","id":"8nverxbc"}
{"time":"2025-07-31T22:55:49.665476743+08:00","level":"INFO","msg":"stream: started","id":"8nverxbc"}
{"time":"2025-07-31T22:55:49.665509534+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"8nverxbc"}
{"time":"2025-07-31T22:55:49.665575925+08:00","level":"INFO","msg":"sender: started","stream_id":"8nverxbc"}
{"time":"2025-07-31T22:55:49.665591888+08:00","level":"INFO","msg":"handler: started","stream_id":"8nverxbc"}
{"time":"2025-07-31T22:55:49.666554726+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
