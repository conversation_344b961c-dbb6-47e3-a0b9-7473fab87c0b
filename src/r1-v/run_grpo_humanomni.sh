cd src/r1-v

export PYTHONPATH=/data/wuyang/R1-Omni-main:/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1:$PYTHONPATH
export DEBUG_MODE="true" # Enable Debug if you want to see the rollout of model during RL
# export LOG_PATH="./logs/humanomni_emotion_emer_1format_withpath_withchoice.txt"
export LOG_PATH="./logs/humanomni_emotion_emer_1format_withpath_withchoice_EVAL.txt"
export HF_HOME=/mnt/data/jiaxing.zjx/cache/huggingface/
mkdir -p ./logs

# 打印日志路径以确认
echo "Log path set to: $LOG_PATH"
# export CUDA_VISIBLE_DEVICES="2,3"
    # --output_dir /data/wuyang/R1-Omni-main/Outputs/test_humanomni_emer_1format_withpath_withchoice/ \
        # --save_only_model true \
            # --resume_from_checkpoint /data/wuyang/R1-Omni-main/Outputs/eval_test/checkpoint-1500 \
WANDB_MODE=offline torchrun --nproc_per_node="1" \
    --nnodes="1" \
    --node_rank="0" \
    --master_addr="127.0.0.1" \
    --master_port="12346" \
    /data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py \
    --output_dir /data/wuyang/R1-Omni-main/Outputs/eval_test \
    --model_name_or_path /data/wuyang/PLM/EMER-SFT-0.5B \
    --dataset_name /data/wuyang/merged_dataset.json \
    --dataset_train_split train \
    # --dataset_test_split validation \
    --deepspeed local_scripts/zero3.json \
    --max_prompt_length 512 \
    --max_completion_length 512 \
    --per_device_train_batch_size 1 \
    --per_device_eval_batch_size 1 \
    --gradient_accumulation_steps 2 \
    --logging_steps 1 \
    --bf16 \
    --report_to wandb \
    --gradient_checkpointing false \
    --attn_implementation flash_attention_2 \
    --max_pixels 401408 \
    --num_train_epochs 2 \
    --run_name Qwen2-VL-2B-GRPO-emotion \
    --save_strategy steps \
    --save_steps 500 \
    --eval_strategy no \
    # --eval_steps 500 \
    # --metric_for_best_model eval_reward\
    # --greater_is_better true \
    # --load_best_model_at_end true \
    --num_generations 8   # number of outputs G in grpo, reduce it would lead to faster training and smaller memory cost but higher variance  